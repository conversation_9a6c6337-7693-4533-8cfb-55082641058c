
'use client';

import { useState, useEffect, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { Repeat, CheckCircle, XCircle, Play, Eye } from 'lucide-react';
import { cn } from '@/lib/utils';
import Image from 'next/image';

type TestStage = 'start' | 'playing' | 'result';

interface IshiharaPlate {
  id: number;
  imageUrl: string;
  correctAnswer: string;
}

const plates: IshiharaPlate[] = [
    { id: 1, imageUrl: '/images/image-1-74-removebg-preview.webp', correctAnswer: '74' },
    { id: 2, imageUrl: '/images/image-2-25-removebg-preview.webp', correctAnswer: '25' },
    { id: 3, imageUrl: '/images/image-3-7-removebg-preview.webp', correctAnswer: '7' },
    { id: 4, imageUrl: '/images/image-4-5-removebg-preview.webp', correctAnswer: '5' },
    { id: 5, imageUrl: '/images/image-5-74-removebg-preview.webp', correctAnswer: '74' },
    { id: 6, imageUrl: '/images/image-6-45-removebg-preview.webp', correctAnswer: '45' },
    { id: 7, imageUrl: '/images/image-7-3-removebg-preview.webp', correctAnswer: '3' },
    { id: 8, imageUrl: '/images/image-8-74-removebg-preview.webp', correctAnswer: '74' },
    { id: 9, imageUrl: '/images/image-9-97-removebg-preview.webp', correctAnswer: '97' },
    { id: 10, imageUrl: '/images/image-10-8-removebg-preview.webp', correctAnswer: '8' },
    { id: 11, imageUrl: '/images/image-11-29-removebg-preview.webp', correctAnswer: '29' },
    { id: 12, imageUrl: '/images/image-12-42-removebg-preview.webp', correctAnswer: '42' },
    { id: 13, imageUrl: '/images/image-13-2-removebg-preview.webp', correctAnswer: '2' },
    { id: 14, imageUrl: '/images/image-14-16-removebg-preview.webp', correctAnswer: '16' },
    { id: 15, imageUrl: '/images/image-15-5-removebg-preview.webp', correctAnswer: '5' },
    { id: 16, imageUrl: '/images/image-16-6-removebg-preview.webp', correctAnswer: '6' },
];


function shuffleArray<T>(array: T[]): T[] {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

export function ColorBlindnessTestTool() {
  const [stage, setStage] = useState<TestStage>('start');
  const [sessionPlates, setSessionPlates] = useState<IshiharaPlate[]>([]);
  const [currentPlateIndex, setCurrentPlateIndex] = useState(0);
  const [userAnswer, setUserAnswer] = useState('');
  const [score, setScore] = useState({ correct: 0, incorrect: 0 });

  const startTest = () => {
    setScore({ correct: 0, incorrect: 0 });
    setCurrentPlateIndex(0);
    setUserAnswer('');
    setSessionPlates(shuffleArray(plates));
    setStage('playing');
  };
  
  const handleAnswerSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const answer = userAnswer.trim();
    if (answer === '') return;

    const currentPlate = sessionPlates[currentPlateIndex];
    const isCorrect = (answer.toLowerCase() === currentPlate.correctAnswer.toLowerCase()) || 
                      (currentPlate.correctAnswer === 'nothing' && answer === '0');
    
    if (isCorrect) {
      setScore(prev => ({ ...prev, correct: prev.correct + 1 }));
    } else {
      setScore(prev => ({ ...prev, incorrect: prev.incorrect + 1 }));
    }
    
    // Move to next plate or show results
    setUserAnswer('');
    if (currentPlateIndex < sessionPlates.length - 1) {
      setCurrentPlateIndex(prev => prev + 1);
    } else {
      setStage('result');
    }
  };
  
  const resetTest = () => {
    setStage('start');
  };

  const currentPlate = sessionPlates[currentPlateIndex];
  const progress = (currentPlateIndex / sessionPlates.length) * 100;
  
  const renderContent = () => {
    switch(stage) {
      case 'start':
        return (
          <div className="text-center space-y-6 flex flex-col items-center">
            <Eye className="h-16 w-16 text-primary" />
            <h3 className="text-xl font-semibold">هل أنت مستعد لاختبار رؤيتك للألوان؟</h3>
            <p className="text-muted-foreground">سيُعرض عليك سلسلة من الصور. أدخل الرقم الذي تراه في كل صورة.</p>
            <Button size="lg" onClick={startTest}>
              <Play className="ml-2 h-5 w-5" />
              ابدأ الاختبار
            </Button>
          </div>
        );

      case 'playing':
        if (!currentPlate) return null;
        return (
          <div className="space-y-6">
            <div className="mb-4">
              <Progress value={progress} />
              <div className="flex justify-between items-center mt-2 text-sm text-muted-foreground">
                <p>الصورة {currentPlateIndex + 1} من {sessionPlates.length}</p>
                <div className="flex items-center gap-4">
                    <span className="flex items-center gap-1 text-green-600 font-bold"><CheckCircle size={16}/> {score.correct}</span>
                    <span className="flex items-center gap-1 text-red-600 font-bold"><XCircle size={16}/> {score.incorrect}</span>
                </div>
              </div>
            </div>
            
            <div className="bg-muted p-4 rounded-lg flex justify-center items-center">
                <Image 
                    src={currentPlate.imageUrl}
                    alt={`لوحة اختبار عمى الألوان رقم ${currentPlate.id}`}
                    width={243}
                    height={243}
                    unoptimized
                />
            </div>

            <form onSubmit={handleAnswerSubmit} className="space-y-4">
                <Input
                    type="number"
                    value={userAnswer}
                    onChange={(e) => setUserAnswer(e.target.value)}
                    placeholder="ما هو الرقم الذي تراه؟ (أدخل 0 إذا لم ترَ شيئًا)"
                    className="text-center text-lg h-12"
                    autoFocus
                />
                <Button type="submit" className="w-full" disabled={userAnswer.trim() === ''}>
                    {currentPlateIndex === sessionPlates.length - 1 ? 'إنهاء الاختبار' : 'تأكيد الإجابة'}
                </Button>
            </form>
          </div>
        );
        
      case 'result':
        const accuracy = Math.round((score.correct / sessionPlates.length) * 100);
        let message;
        if (accuracy >= 90) message = "رؤيتك للألوان تبدو طبيعية.";
        else if (accuracy >= 70) message = "قد يكون لديك ضعف بسيط في تمييز الألوان.";
        else message = "تشير النتائج إلى احتمال وجود ضعف في رؤية الألوان. يُنصح بشدة بزيارة طبيب عيون.";

        return (
          <div className="text-center space-y-4 flex flex-col items-center">
            <h3 className="text-2xl font-bold">اكتمل الاختبار!</h3>
            <p className="text-lg">نتيجتك النهائية هي:</p>
            <p className="text-5xl font-bold font-mono text-primary">{score.correct} / {sessionPlates.length}</p>
            <p className="text-muted-foreground font-semibold">{message}</p>
             <div className="w-full pt-4 mt-4 border-t">
                <p className="text-sm text-red-600 font-bold">إخلاء مسؤولية مهم:</p>
                <p className="text-xs text-muted-foreground">هذا الاختبار هو أداة فحص أولية ولا يغني عن التشخيص الطبي المتخصص. إذا كانت لديك أي مخاوف بشأن رؤيتك للألوان، يرجى استشارة طبيب عيون.</p>
            </div>
            <Button onClick={resetTest} variant="outline" className="w-full max-w-xs mt-4">
                <Repeat className="ml-2 h-4 w-4" />
                أعد الاختبار
            </Button>
          </div>
        );
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-center gap-2">
            <Eye className="h-6 w-6 text-primary" />
            اختبار عمى الألوان (إيشيهارا)
        </CardTitle>
        <CardDescription className="text-center">
            اختبر قدرتك على تمييز الألوان من خلال هذه السلسلة من اللوحات.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {renderContent()}
      </CardContent>
    </Card>
  );
}
