
'use client';

import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Repeat, CheckCircle, XCircle, BrainCircuit, Play, Trophy, ChevronLeft } from 'lucide-react';
import { cn } from '@/lib/utils';
import { islamicQuestions } from '@/lib/islamic-quiz-questions';

type Answer = { text: string; isCorrect: boolean; explanation?: string };
type Question = { text: string; answers: Answer[]; category: string };
type TestStage = 'start' | 'playing' | 'result';

const QUESTIONS_PER_SESSION = 20;

// Helper to shuffle an array
function shuffleArray<T>(array: T[]): T[] {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}

export function IslamicQuizTool() {
  const [stage, setStage] = useState<TestStage>('start');
  const [sessionQuestions, setSessionQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [feedback, setFeedback] = useState<{ message: string; isCorrect: boolean; explanation?: string } | null>(null);
  const [score, setScore] = useState({ correct: 0, incorrect: 0 });

  const startQuiz = () => {
    setScore({ correct: 0, incorrect: 0 });
    setFeedback(null);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setSessionQuestions(shuffleArray(islamicQuestions).slice(0, QUESTIONS_PER_SESSION));
    setStage('playing');
  };
  
  const goToNextQuestion = () => {
    setFeedback(null);
    setSelectedAnswer(null);
    if (currentQuestionIndex < sessionQuestions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      setStage('result');
    }
  };

  const handleAnswerSubmit = () => {
    if (selectedAnswer === null) return;

    const currentQuestion = sessionQuestions[currentQuestionIndex];
    const answerObject = currentQuestion.answers.find(a => a.text === selectedAnswer);
    
    if (answerObject) {
      if (answerObject.isCorrect) {
        setScore(prev => ({ ...prev, correct: prev.correct + 1 }));
        setFeedback({ message: 'إجابة صحيحة!', isCorrect: true, explanation: answerObject.explanation });
      } else {
        setScore(prev => ({ ...prev, incorrect: prev.incorrect + 1 }));
        const correctAnswer = currentQuestion.answers.find(a => a.isCorrect)?.text;
        setFeedback({ message: `خطأ. الإجابة الصحيحة هي: ${correctAnswer}`, isCorrect: false, explanation: answerObject.explanation });
      }
    }
  };
  
  const resetQuiz = () => {
    setStage('start');
  };

  const currentQuestion = sessionQuestions[currentQuestionIndex];

  const shuffledAnswers = useMemo(() => {
    if (!currentQuestion) return [];
    return shuffleArray(currentQuestion.answers);
  }, [currentQuestion]);

  const progress = (currentQuestionIndex / sessionQuestions.length) * 100;
  const isAnswered = feedback !== null;

  const renderContent = () => {
    switch(stage) {
      case 'start':
        return (
          <div className="text-center space-y-6 flex flex-col items-center">
            <BrainCircuit className="h-16 w-16 text-primary" />
            <h3 className="text-xl font-semibold">اختبر معلوماتك الدينية</h3>
            <p className="text-muted-foreground">استعد لاختبار ممتع ومفيد يتكون من {QUESTIONS_PER_SESSION} سؤالًا عشوائيًا في مختلف جوانب الدين الإسلامي.</p>
            <Button size="lg" onClick={startQuiz}>
              <Play className="ml-2 h-5 w-5" />
              ابدأ الاختبار
            </Button>
          </div>
        );

      case 'playing':
        if (!currentQuestion) return null;
        return (
          <div className="space-y-6">
            <div className="mb-4">
              <Progress value={progress} />
              <div className="flex justify-between items-center mt-2 text-sm text-muted-foreground">
                <p>
                  السؤال {currentQuestionIndex + 1} من {sessionQuestions.length}
                  <span className="mx-2">|</span>
                  <span className="font-semibold text-primary">{currentQuestion.category}</span>
                </p>
                <div className="flex items-center gap-4">
                    <span className="flex items-center gap-1 text-green-600 font-bold"><CheckCircle size={16}/> {score.correct}</span>
                    <span className="flex items-center gap-1 text-red-600 font-bold"><XCircle size={16}/> {score.incorrect}</span>
                </div>
              </div>
            </div>
            <h3 className="text-lg font-semibold text-center leading-relaxed">{currentQuestion.text}</h3>
            <RadioGroup
              key={currentQuestionIndex}
              value={selectedAnswer || ""}
              onValueChange={setSelectedAnswer}
              className="space-y-3"
              disabled={isAnswered}
            >
              {shuffledAnswers.map((answer, index) => {
                const isCorrectAnswer = answer.isCorrect;
                const isSelectedAnswer = selectedAnswer === answer.text;
                
                return (
                  <Label 
                    key={index} 
                    dir="rtl"
                    className={cn(
                        "flex items-center gap-x-3 p-4 border rounded-lg transition-colors cursor-pointer hover:bg-muted/50",
                        !isAnswered && "has-[input:checked]:bg-primary/10 has-[input:checked]:border-primary",
                        isAnswered && isCorrectAnswer && "bg-green-100 border-green-400 text-green-800",
                        isAnswered && !isCorrectAnswer && isSelectedAnswer && "bg-red-100 border-red-400 text-red-800",
                        isAnswered && "cursor-not-allowed opacity-80"
                    )}
                  >
                    <RadioGroupItem 
                      value={answer.text} 
                      id={`q${currentQuestionIndex}-a${index}`}
                      className="border-primary"
                    />
                    <span className="flex-1">{answer.text}</span>
                     {isAnswered && isCorrectAnswer && <CheckCircle className="h-5 w-5 text-green-600" />}
                     {isAnswered && !isCorrectAnswer && isSelectedAnswer && <XCircle className="h-5 w-5 text-red-600" />}
                  </Label>
                )
              })}
            </RadioGroup>

            {!isAnswered ? (
              <Button onClick={handleAnswerSubmit} disabled={selectedAnswer === null} className="w-full">
                تأكيد الإجابة
              </Button>
            ) : (
                <>
                    {feedback?.explanation && (
                        <div className="p-3 bg-blue-50 border-l-4 border-blue-400 text-blue-800 text-sm">
                            <strong>توضيح:</strong> {feedback.explanation}
                        </div>
                    )}
                    <Button onClick={goToNextQuestion} className="w-full">
                        السؤال التالي <ChevronLeft className="mr-2 h-4 w-4" />
                    </Button>
                </>
            )}
          </div>
        );
        
      case 'result':
        const percentage = Math.round((score.correct / sessionQuestions.length) * 100);
        let message;
        if (percentage >= 90) message = "ممتاز! معلوماتك الدينية قوية جدًا.";
        else if (percentage >= 70) message = "جيد جدًا! لديك معرفة جيدة بالأساسيات.";
        else if (percentage >= 50) message = "لا بأس، يمكنك دائمًا تعلم المزيد.";
        else message = "فرصة جيدة لمراجعة بعض المعلومات الأساسية. واصل التعلم!";

        return (
          <div className="text-center space-y-4 flex flex-col items-center">
            <Trophy className="h-16 w-16 text-yellow-500" />
            <h3 className="text-2xl font-bold">اكتمل الاختبار!</h3>
            <p className="text-lg">نتيجتك النهائية هي:</p>
            <p className="text-5xl font-bold font-mono text-primary">{score.correct} / {sessionQuestions.length}</p>
            <p className="text-muted-foreground font-semibold">{message}</p>
            <Button onClick={resetQuiz} variant="outline" className="w-full max-w-xs mt-4">
                <Repeat className="ml-2 h-4 w-4" />
                أعد الاختبار
            </Button>
          </div>
        );
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-center gap-2">
            <BrainCircuit className="h-6 w-6 text-primary" />
            الاختبار الديني
        </CardTitle>
        <CardDescription className="text-center">
            اختبر معرفتك في القرآن والسيرة والفقه والعقيدة.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {renderContent()}
      </CardContent>
    </Card>
  );
}
